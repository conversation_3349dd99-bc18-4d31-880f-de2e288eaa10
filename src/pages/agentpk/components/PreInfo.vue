<template>
  <div class="pre-info-container">
    <div class="memo-title">预获取资讯</div>
    <div class="memo-content">
      <div class="coming-soon-text">
        即将推出，敬请期待
      </div>
    </div>
    <div class="memo-add-btn" @click="handleAddClick">
    </div>
  </div>
</template>

<script setup lang="ts">
// Emits定义
const emit = defineEmits<{
  addPreInfo: [];
}>();

// 处理添加按钮点击事件
const handleAddClick = () => {
  console.log('预获取资讯添加按钮被点击');
  emit('addPreInfo');
};
</script>

<style lang="scss" scoped>
.pre-info-container {
  height: 50px;
  width: 100%;
  background: #f3f9ff; // 更浅的蓝色底，与行业备忘录一致
  border: 1px solid #1565c0; // 与行业备忘录一致的深蓝色边框
  border-radius: 12px;
  display: flex;
  align-items: center;
  padding: 2px 16px;
  box-sizing: border-box;
  position: relative;

  .memo-title {
    color: #1565c0; // 与行业备忘录一致的深蓝色
    font-size: 26px;
    font-weight: 400; // 减小字体粗细
    white-space: nowrap;
    margin-right: 16px;
  }

  .memo-content {
    flex: 1;
    height: 100%;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: flex-start;

    .coming-soon-text {
      font-size: 26px;
      color: #1565c0;
      font-weight: 400;
      font-style: italic;
      text-align: left;
    }
  }

  .memo-add-btn {
    width: 50px;
    height: 30px;
    background: transparent;
    border: 1px solid #1565c0;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #1565c0;
    font-size: 16px;
    font-weight: 400;
    padding: 2px;

    &::after {
      content: "展开";
    }
  }
}
</style>
