<template>
  <div class="industry-memo-container">
    <div class="memo-title">行业备忘录</div>
    <div class="memo-content">
      <div ref="tagsContainer" class="memo-tags-container">
        <div
          v-for="(memo, index) in industryMemos"
          :key="index"
          class="memo-tag"
        >
          <span class="memo-text" @click="handleMemoItemClick(memo)">{{ memo.content }}</span>
          <!-- <span class="memo-delete-btn" @click="handleDeleteClick(memo)">×</span> -->
        </div>
        <div v-if="industryMemos.length === 0 && !isLoading && hasReceived" class="empty-memo">
          即将推出，敬请期待
        </div>
        <div v-if="isLoading" class="empty-memo">
          加载中...
        </div>
      </div>
    </div>
    <div class="memo-add-btn" @click="handleAddClick">
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

// 行业备忘录数据接口
interface IIndustryMemo {
  id: string;
  content: string;
  created_at: string;
  updated_at?: string;
}

// Emits定义
const emit = defineEmits<{
  addIndustryMemo: [];
  editIndustryMemo: [memo: IIndustryMemo];
  deleteIndustryMemo: [memo: IIndustryMemo];
}>();

// 响应式数据
const industryMemos = ref<IIndustryMemo[]>([]);
const isLoading = ref(false);
const hasReceived = ref(false);
const tagsContainer = ref<HTMLElement>();

// 获取行业备忘录数据
const fetchIndustryMemos = async () => {
  try {
    isLoading.value = true;
    console.log('📤 [IndustryMemo] 开始获取行业备忘录数据');

    // TODO: 这里后续需要调用后端API获取行业备忘录数据
    // 暂时使用写死的示例数据
    await new Promise<void>(resolve => {
      setTimeout(resolve, 500);
    });

    // 写死的行业备忘录示例数据
    industryMemos.value = [
      {
        id: 'industry_memo_1',
        content: 'AI大模型技术发展趋势',
        created_at: '2024-01-15T10:30:00Z',
        updated_at: '2024-01-15T10:30:00Z'
      },
      {
        id: 'industry_memo_2',
        content: '云计算架构最佳实践',
        created_at: '2024-01-14T14:20:00Z',
        updated_at: '2024-01-14T14:20:00Z'
      },
      {
        id: 'industry_memo_3',
        content: '前端框架性能优化',
        created_at: '2024-01-13T09:15:00Z',
        updated_at: '2024-01-13T09:15:00Z'
      },
      {
        id: 'industry_memo_4',
        content: '微服务架构设计原则',
        created_at: '2024-01-12T16:45:00Z',
        updated_at: '2024-01-12T16:45:00Z'
      },
      {
        id: 'industry_memo_5',
        content: '数据库索引优化策略',
        created_at: '2024-01-11T11:30:00Z',
        updated_at: '2024-01-11T11:30:00Z'
      },
      {
        id: 'industry_memo_6',
        content: 'DevOps流程自动化',
        created_at: '2024-01-10T13:20:00Z',
        updated_at: '2024-01-10T13:20:00Z'
      }
    ];

    hasReceived.value = true;
    console.log('✅ [IndustryMemo] 行业备忘录数据获取成功:', industryMemos.value);
  } catch (error) {
    console.error('❌ [IndustryMemo] 获取行业备忘录数据失败:', error);
    industryMemos.value = [];
    hasReceived.value = true;
  } finally {
    isLoading.value = false;
  }
};

// 处理备忘录项点击事件
const handleMemoItemClick = (memo: IIndustryMemo) => {
  console.log('🔄 [IndustryMemo] 备忘录项被点击:', memo);
  emit('editIndustryMemo', memo);
};

// 处理删除按钮点击事件
const handleDeleteClick = (memo: IIndustryMemo) => {
  console.log('🔄 [IndustryMemo] 删除备忘录:', memo);
  emit('deleteIndustryMemo', memo);
};

// 处理添加按钮点击事件
const handleAddClick = () => {
  console.log('行业备忘录添加按钮被点击');
  emit('addIndustryMemo');
};

// 组件挂载时获取数据
onMounted(() => {
  void fetchIndustryMemos();
});

// 暴露方法供父组件调用
defineExpose({
  fetchIndustryMemos,
});
</script>

<style lang="scss" scoped>
.industry-memo-container {
  height: 50px;
  width: 100%;
  background: #f3f9ff; // 更浅的蓝色底
  border: 1px solid #1565c0; // 更深的蓝色边框
  border-radius: 12px;
  display: flex;
  align-items: center;
  padding: 2px 16px;
  box-sizing: border-box;
  position: relative;
  .memo-title {
    color: #1565c0; // 更深的蓝色，与边框颜色统一
    font-size: 26px;
    font-weight: 400; // 减小字体粗细
    white-space: nowrap;
    margin-right: 16px;
  }

  .memo-content {
    flex: 1;
    height: 100%;
    overflow: hidden;

    .memo-tags-container {
      display: flex;
      align-items: center;
      height: 100%;
      overflow-x: auto;
      overflow-y: hidden;
      gap: 12px;
      padding: 4px 0;

      // 隐藏滚动条但保持滚动功能
      scrollbar-width: none; // Firefox
      -ms-overflow-style: none; // IE/Edge
      &::-webkit-scrollbar {
        display: none; // Chrome/Safari
      }

      .memo-tag {
        background: #e3f2fd; // 比父组件略深的浅蓝色底
        border-radius: 10px;
        padding: 2px 8px;
        font-size: 26px;
        color: #1565c0;
        white-space: nowrap;
        flex-shrink: 0;
        border: 1px solid #90caf9;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;

        .memo-text {
          flex: 1;
          cursor: pointer;
        }

        .memo-delete-btn {
          color: #f44336;
          font-size: 20px;
          font-weight: bold;
          cursor: pointer;
          padding: 0 4px;
          border-radius: 50%;
          transition: all 0.2s ease;

          &:hover {
            background: rgba(244, 67, 54, 0.1);
          }
        }
      }

      .empty-memo {
        color: #64b5f6;
        font-size: 20px;
        font-style: italic;
        white-space: nowrap;
      }
    }
  }

  .memo-add-btn {
    width: 50px;
    height: 30px;
    background: transparent;
    border: 1px solid #1565c0;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #1565c0;
    font-size: 16px;
    font-weight: 400;
    padding: 2px;

    &::after {
      content: "添加";
    }
  }
}
</style>
